#!/usr/bin/env python3
"""
Fix the attack message format in bot.py
"""

def fix_attack_message():
    # Read the file
    with open('bot.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find and replace the attack message
    old_message = '''            await update.message.reply_text(
                f"🚀 **Attack Launching...** 🚀\\n\\n"
                f"🎯 **Target Info:**\\n"
                f"• Host: {host}\\n"
                f"• Port: {port}\\n"
                f"• Method: {method.upper()}\\n"
                f"• Duration: {duration}s\\n\\n"
                f"🌍 **Location:**\\n"
                f"• City: {city}\\n"
                f"• Country: {country}\\n"
                f"• ISP: {isp}\\n\\n"
                f"📊 **System Status:**\\n"
                f"• Slots: {active_count + 1}/{max_slots}\\n"
                f"• Attack ID: {attack_id}\\n\\n"
                f"⚡ **Attack is starting in background...**\\n"
                f"💡 Use /status to monitor or /stop to stop all"
            )'''
    
    new_message = '''            await update.message.reply_text(
                f"```\\n"
                f"🐬 Attack Successfully 🐬\\n"
                f"• Attack ID: {attack_id}\\n"
                f"• Host: {target}\\n"
                f"• Port: {port}\\n"
                f"• Duration: {duration}s\\n"
                f"• ASN: {asn if 'asn' in locals() else 'Unknown'}\\n"
                f"• City: {city}\\n"
                f"• Country: {country}\\n"
                f"• ISP: {isp}\\n"
                f"• Org: {isp}\\n"
                f"• Region: {region if 'region' in locals() else 'Unknown'}\\n"
                f"• Timezone: {timezone if 'timezone' in locals() else 'Unknown'}\\n"
                f"• Slots: {active_count + 1}/{max_slots}\\n"
                f"```",
                parse_mode='Markdown'
            )'''
    
    # Try different variations to handle encoding issues
    variations = [
        'f"🚀 **Attack Launching...** 🚀\\n\\n"',
        'f"� **Attack Launching...** 🚀\\n\\n"',
        'f"🚀 **Attack Launching...** 🚀\\n\\n"'
    ]
    
    found = False
    for variation in variations:
        if variation in content:
            print(f"Found variation: {variation}")
            # Replace the entire message block
            start_marker = "await update.message.reply_text("
            end_marker = ")"
            
            lines = content.split('\\n')
            new_lines = []
            in_message_block = False
            indent_level = 0
            
            for line in lines:
                if start_marker in line and variation.replace('f"', '').replace('"', '') in line:
                    in_message_block = True
                    indent_level = len(line) - len(line.lstrip())
                    new_lines.append(' ' * indent_level + 'await update.message.reply_text(')
                    new_lines.append(' ' * (indent_level + 4) + 'f"```\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"🐬 Attack Successfully 🐬\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Attack ID: {attack_id}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Host: {target}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Port: {port}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Duration: {duration}s\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• ASN: {asn if \'asn\' in locals() else \'Unknown\'}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• City: {city}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Country: {country}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• ISP: {isp}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Org: {isp}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Region: {region if \'region\' in locals() else \'Unknown\'}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Timezone: {timezone if \'timezone\' in locals() else \'Unknown\'}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"• Slots: {active_count + 1}/{max_slots}\\n"')
                    new_lines.append(' ' * (indent_level + 4) + 'f"```",')
                    new_lines.append(' ' * (indent_level + 4) + 'parse_mode=\'Markdown\'')
                    new_lines.append(' ' * indent_level + ')')
                    found = True
                elif in_message_block and line.strip() == ')':
                    in_message_block = False
                    # Skip this line as we already added the closing
                elif not in_message_block:
                    new_lines.append(line)
            
            if found:
                content = '\\n'.join(new_lines)
                break
    
    if not found:
        print("Could not find the attack message to replace")
        return False
    
    # Write the file back
    with open('bot.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Successfully updated attack message format!")
    return True

if __name__ == "__main__":
    fix_attack_message()
