#!/usr/bin/env python3
"""
Simple fix for attack message
"""

# Read the file
with open('bot.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Replace the attack message with simple string replacement
old_part = '''f"🚀 **Attack Launching...** 🚀\\n\\n"
                f"🎯 **Target Info:**\\n"
                f"• Host: {host}\\n"
                f"• Port: {port}\\n"
                f"• Method: {method.upper()}\\n"
                f"• Duration: {duration}s\\n\\n"
                f"🌍 **Location:**\\n"
                f"• City: {city}\\n"
                f"• Country: {country}\\n"
                f"• ISP: {isp}\\n\\n"
                f"📊 **System Status:**\\n"
                f"• Slots: {active_count + 1}/{max_slots}\\n"
                f"• Attack ID: {attack_id}\\n\\n"
                f"⚡ **Attack is starting in background...**\\n"
                f"💡 Use /status to monitor or /stop to stop all"'''

new_part = '''f"```\\n"
                f"🐬 Attack Successfully 🐬\\n"
                f"• Attack ID: {attack_id}\\n"
                f"• Host: {target}\\n"
                f"• Port: {port}\\n"
                f"• Duration: {duration}s\\n"
                f"• ASN: {asn if 'asn' in locals() else 'Unknown'}\\n"
                f"• City: {city}\\n"
                f"• Country: {country}\\n"
                f"• ISP: {isp}\\n"
                f"• Org: {isp}\\n"
                f"• Region: {region if 'region' in locals() else 'Unknown'}\\n"
                f"• Timezone: {timezone if 'timezone' in locals() else 'Unknown'}\\n"
                f"• Slots: {active_count + 1}/{max_slots}\\n"
                f"```",
                parse_mode='Markdown'
            )
            
            # Also need to get ASN, region, timezone info
            # Get ASN and location info
            try:
                response = requests.get(f"https://ipinfo.io/{ip}/json", timeout=5).json()
                asn = response.get('org', '').split()[0] if 'org' in response else 'Unknown'
                region = response.get('region', 'Unknown')
                timezone = response.get('timezone', 'Unknown')
            except:
                asn = region = timezone = 'Unknown'
            
            # Send immediate response - bot is responsive!
            await update.message.reply_text(
                f"```\\n"
                f"🐬 Attack Successfully 🐬\\n"
                f"• Attack ID: {attack_id}\\n"
                f"• Host: {target}\\n"
                f"• Port: {port}\\n"
                f"• Duration: {duration}s\\n"
                f"• ASN: {asn}\\n"
                f"• City: {city}\\n"
                f"• Country: {country}\\n"
                f"• ISP: {isp}\\n"
                f"• Org: {isp}\\n"
                f"• Region: {region}\\n"
                f"• Timezone: {timezone}\\n"
                f"• Slots: {active_count + 1}/{max_slots}\\n"
                f"```",
                parse_mode='Markdown'
            )'''

# Try to find and replace the corrupted character version
if "🚀 **Attack Launching..." in content:
    print("Found rocket emoji version")
    content = content.replace(old_part, new_part)
elif "� **Attack Launching..." in content:
    print("Found corrupted character version")
    # Replace the corrupted version
    old_corrupted = old_part.replace("🚀", "�")
    content = content.replace(old_corrupted, new_part)
else:
    print("Could not find attack message to replace")

# Write back
with open('bot.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("✅ Attack message updated!")
